# 无限画布便签应用

这是一个基于 React + TypeScript + Vite 构建的无限画布应用，支持添加和管理便签功能。

## 功能特性

### 🎨 无限画布

- **无限缩放和平移** - 使用鼠标滚轮缩放，拖拽移动画布
- **网格背景** - 提供视觉参考的网格系统
- **流畅动画** - 优化的性能和流畅的交互体验

### 📝 便签功能

- **一键添加** - 点击工具栏的 ➕ 按钮即可在画布中心添加便签
- **标题编辑** - 点击便签标题即可编辑，支持键盘快捷键
- **内容编辑** - 在便签内容区域自由输入文本
- **拖拽移动** - 通过便签头部拖拽移动位置
- **大小调整** - 拖拽右下角调整便签大小
- **一键删除** - 点击便签右上角的 ✕ 按钮删除
- **多种颜色** - 自动随机分配不同颜色的便签

### ⌨️ 键盘快捷键

- `Ctrl/⌘ + +` - 放大画布
- `Ctrl/⌘ + -` - 缩小画布
- `Ctrl/⌘ + 0` - 重置画布位置和缩放
- `Enter` - 完成便签标题编辑
- `Escape` - 取消便签标题编辑

## 使用方法

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 运行测试

```bash
npm test
```

## 便签操作指南

1. **添加便签**

   - 点击顶部工具栏的 ➕ 按钮
   - 便签会在画布中心位置创建

2. **编辑便签标题**

   - 点击便签顶部的标题文字
   - 输入新标题后按 Enter 确认或点击其他地方
   - 按 Escape 取消编辑

3. **编辑便签内容**

   - 直接在便签内容区域输入文字
   - 支持多行文本

4. **移动便签**

   - 拖拽便签头部（标题区域）移动位置

5. **调整便签大小**

   - 拖拽便签右下角的调整手柄

6. **删除便签**
   - 点击便签右上角的 ✕ 按钮

## 技术栈

- **React 19** - 用户界面框架
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 快速的构建工具
- **Ant Design** - UI 组件库
- **react-rnd** - 拖拽和调整大小功能
- **Lodash** - 实用工具库

## 项目结构

```
src/
├── components/
│   ├── InfiniteCanvas.tsx      # 主画布组件
│   ├── InfiniteCanvas.css      # 画布样式
│   ├── CanvasToolbar.tsx       # 工具栏组件
│   ├── CanvasGrid.tsx          # 网格组件
│   ├── StickyNote.tsx          # 便签组件
│   ├── StickyNote.test.tsx     # 便签组件测试
│   └── CanvasConstants.ts      # 常量定义
├── App.tsx                     # 应用入口
└── main.tsx                    # 主入口文件
```

## 开发说明

### 便签数据结构

```typescript
interface StickyNoteData {
  id: string; // 唯一标识符
  title: string; // 便签标题
  content: string; // 便签内容
  x: number; // X坐标位置
  y: number; // Y坐标位置
  width: number; // 宽度
  height: number; // 高度
  color: string; // 便签颜色
}
```

### 性能优化

- 使用 `useCallback` 和 `useMemo` 优化渲染性能
- 节流处理鼠标滚轮事件
- CSS 变量控制样式，减少重排重绘
- GPU 加速的 CSS 变换

## 许可证

MIT License
