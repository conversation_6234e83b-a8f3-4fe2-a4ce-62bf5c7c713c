import React, { useState, useRef, useCallback, useEffect } from "react";
import { Button, Input } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { Rnd } from "react-rnd";

// 便签数据接口
export interface StickyNoteData {
  id: string; // 唯一标识符
  title: string; // 便签标题
  content: string; // 便签内容
  x: number; // X坐标位置
  y: number; // Y坐标位置
  width: number; // 宽度
  height: number; // 高度
  color: string; // 便签颜色
}

// 便签组件属性接口
interface StickyNoteProps {
  data: StickyNoteData; // 便签数据
  onUpdate: (id: string, updates: Partial<StickyNoteData>) => void; // 更新回调
  onDelete: (id: string) => void; // 删除回调
  scale: number; // 画布缩放比例
}

const StickyNote: React.FC<StickyNoteProps> = ({
  data,
  onUpdate,
  onDelete,
  scale,
}) => {
  const [isEditingTitle, setIsEditingTitle] = useState(false); // 是否正在编辑标题
  const [tempTitle, setTempTitle] = useState(data.title); // 临时标题
  const titleInputRef = useRef<any>(null); // 标题输入框引用

  // 处理标题编辑开始
  const handleTitleEditStart = useCallback(() => {
    setIsEditingTitle(true);
    setTempTitle(data.title);
  }, [data.title]);

  // 处理标题编辑完成
  const handleTitleEditFinish = useCallback(() => {
    setIsEditingTitle(false);
    if (tempTitle.trim() !== data.title) {
      onUpdate(data.id, { title: tempTitle.trim() || "无标题" });
    }
  }, [tempTitle, data.title, data.id, onUpdate]);

  // 处理标题输入变化
  const handleTitleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setTempTitle(e.target.value);
    },
    []
  );

  // 处理键盘事件
  const handleTitleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter") {
        handleTitleEditFinish();
      } else if (e.key === "Escape") {
        setIsEditingTitle(false);
        setTempTitle(data.title);
      }
    },
    [handleTitleEditFinish, data.title]
  );

  // 处理内容变化
  const handleContentChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      onUpdate(data.id, { content: e.target.value });
    },
    [data.id, onUpdate]
  );

  // 处理位置和大小变化
  const handleDragStop = useCallback(
    (e: any, d: any) => {
      onUpdate(data.id, { x: d.x, y: d.y });
    },
    [data.id, onUpdate]
  );

  const handleResizeStop = useCallback(
    (e: any, direction: any, ref: any, delta: any, position: any) => {
      onUpdate(data.id, {
        width: ref.offsetWidth,
        height: ref.offsetHeight,
        x: position.x,
        y: position.y,
      });
    },
    [data.id, onUpdate]
  );

  // 处理删除
  const handleDelete = useCallback(() => {
    onDelete(data.id);
  }, [data.id, onDelete]);

  // 自动聚焦标题输入框
  useEffect(() => {
    if (isEditingTitle && titleInputRef.current) {
      titleInputRef.current.focus();
      titleInputRef.current.select();
    }
  }, [isEditingTitle]);

  return (
    <Rnd
      size={{ width: data.width, height: data.height }}
      position={{ x: data.x, y: data.y }}
      onDragStop={handleDragStop}
      onResizeStop={handleResizeStop}
      minWidth={200}
      minHeight={150}
      bounds="parent"
      dragHandleClassName="sticky-note-header"
      className="sticky-note-container"
      style={{
        zIndex: 10,
        // 根据画布缩放调整便签的显示大小，保持便签在不同缩放级别下的可读性
        transform: `scale(${Math.max(0.8, Math.min(1.2, 1 / scale))})`,
        transformOrigin: "0 0",
      }}
    >
      <div className="sticky-note" style={{ backgroundColor: data.color }}>
        {/* 便签头部 */}
        <div className="sticky-note-header">
          <div className="sticky-note-title-container">
            {isEditingTitle ? (
              <Input
                ref={titleInputRef}
                value={tempTitle}
                onChange={handleTitleChange}
                onBlur={handleTitleEditFinish}
                onKeyDown={handleTitleKeyDown}
                className="sticky-note-title-input"
                placeholder="输入标题..."
                maxLength={50}
              />
            ) : (
              <div
                className="sticky-note-title"
                onClick={handleTitleEditStart}
                title="点击编辑标题"
              >
                {data.title || "无标题"}
              </div>
            )}
          </div>
          <Button
            type="text"
            size="small"
            icon={<CloseOutlined />}
            onClick={handleDelete}
            className="sticky-note-close-btn"
            title="删除便签"
          />
        </div>

        {/* 便签内容 */}
        <div className="sticky-note-content">
          <textarea
            value={data.content}
            onChange={handleContentChange}
            placeholder="在这里输入内容..."
            className="sticky-note-textarea"
          />
        </div>

        {/* 调整大小手柄 */}
        <div className="sticky-note-resize-handle" />
      </div>
    </Rnd>
  );
};

export default StickyNote;
