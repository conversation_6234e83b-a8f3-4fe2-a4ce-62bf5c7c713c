.text-block {
  background-color: #feffc8; /* Light yellow background, similar to a sticky note */
  border: 1px solid #fdd835; /* Yellow border */
  border-radius: 4px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Ensures content doesn't spill out */
  position: relative; /* 为缩放句柄设置相对定位上下文 */
  box-sizing: border-box; /* 确保内边距和边框计入总宽高 */
  user-select: none; /* 防止意外选中文本 */
  will-change: transform; /* 告诉浏览器该元素会频繁变换，提前做好准备 */
  transform: translate3d(0, 0, 0); /* 启用硬件加速，防止闪烁 */
}

/* 拖动过程中的样式 */
.react-draggable-dragging {
  opacity: 1 !important; /* 确保拖动时不透明度保持不变 */
  transform: translate3d(0, 0, 0) !important; /* 确保使用硬件加速 */
  transition: none !important; /* 禁用所有过渡效果 */
}

/* 只在静态状态使用过渡效果，不影响拖动性能 */
.text-block:not(:active):not(.react-draggable-dragging):hover {
  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.15); /* 悬停时增强阴影 */
}

.text-block-header {
  background-color: #fff9c4; /* Slightly darker yellow for header */
  padding: 4px 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: grab;
  border-bottom: 1px solid #fdd835;
  -webkit-app-region: no-drag; /* 确保拖动功能正常，避免与操作系统拖动冲突 */
  touch-action: none; /* 防止触摸屏拖动时的问题 */
}

.text-block-title {
  font-weight: bold;
  font-size: 14px;
  color: #5d4037; /* Brownish text color */
}

.text-block-title-input {
  font-weight: bold;
  font-size: 14px;
  border: none;
  outline: none;
  background-color: transparent;
  padding: 0;
  color: #5d4037;
}

.text-block-delete-button.ant-btn-sm {
  padding: 0 4px; /* Adjust padding for smaller button */
  height: auto; /* Adjust height for smaller button */
  line-height: 1; /* Adjust line-height for smaller button */
  border: none;
}

.text-block-delete-button.ant-btn-sm .anticon {
  font-size: 12px; /* Adjust icon size */
}

.text-block-content {
  padding: 8px;
  font-size: 13px;
  border: none;
  outline: none;
  resize: none;
  background-color: transparent;
  flex-grow: 1;
  color: #5d4037;
  line-height: 1.4;
  overflow-y: auto; /* 添加垂直滚动条 */
  width: 100%; /* 确保宽度占满容器 */
  box-sizing: border-box; /* 包括内边距在内的盒模型 */
}

.text-block-content::placeholder {
  color: #a1887f; /* Lighter brownish color for placeholder */
}

/* 隐形缩放区域样式 - 增大热区 */
.text-block-resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 24px; /* 增大宽度 */
  height: 24px; /* 增大高度 */
  cursor: nwse-resize; /* 西北-东南方向的箭头 */
  background: transparent;
  z-index: 10;
}

/* 增加一个正在拖动时的样式类 */
.text-block.resizing {
  opacity: 0.95; /* 提供轻微的视觉反馈 */
}

/* 当鼠标悬停在缩放区域上时添加轻微的颜色变化，作为用户反馈 */
.text-block-resize-handle:hover {
  background-color: rgba(253, 216, 53, 0.2); /* 稍微增加可见度 */
}

/* 为整个便签的右下角添加拖动指示器样式 */
.text-block::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-bottom: 7px solid #fdd835;
  border-right: 7px solid #fdd835;
  border-bottom-right-radius: 2px;
  z-index: 1;
  pointer-events: none; /* 确保不会干扰拖拽和调整大小 */
  border-top: 7px solid transparent;
  border-left: 7px solid transparent;
  opacity: 0.4;
  pointer-events: none; /* 确保不会干扰鼠标事件 */
}
