import React, { useState, useRef, useEffect, memo, useCallback } from "react";
import { Input, Button } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { Rnd } from "react-rnd"; // 导入 react-rnd 组件
import type { TextBlockData } from "./CanvasConstants";
import "./TextBlock.css";

interface TextBlockProps {
  data: TextBlockData;
  onUpdate: (id: string, data: Partial<TextBlockData>) => void;
  onDelete: (id: string) => void;
}

// 使用 memo 包裹组件，避免不必要的重渲染
const TextBlock: React.FC<TextBlockProps> = memo(
  ({ data, onUpdate, onDelete }) => {
    const [isEditingTitle, setIsEditingTitle] = useState(false);
    const [title, setTitle] = useState(data.title);
    const [content, setContent] = useState(data.content);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // 初始尺寸和位置
    const width = data.width || 150;
    const height = data.height || 100;
    const x = data.x || 0;
    const y = data.y || 0;

    useEffect(() => {
      setTitle(data.title);
    }, [data.title]);

    useEffect(() => {
      setContent(data.content);
    }, [data.content]);

    // 处理拖拽结束事件 - 直接更新位置，避免闪烁
    const handleDragStop = useCallback(
      (_e: any, d: { x: number; y: number }) => {
        // 直接更新位置，不使用 requestAnimationFrame，避免延迟导致的闪烁
        onUpdate(data.id, { x: d.x, y: d.y });
      },
      [data.id, onUpdate]
    );

    // 处理缩放结束事件
    const handleResizeStop = useCallback(
      (
        _e: any,
        _direction: any,
        ref: HTMLElement,
        _delta: any,
        position: { x: number; y: number }
      ) => {
        onUpdate(data.id, {
          width: parseInt(ref.style.width, 10),
          height: parseInt(ref.style.height, 10),
          x: position.x,
          y: position.y,
        });
      },
      [data.id, onUpdate]
    );

    const handleTitleDoubleClick = useCallback((e: React.MouseEvent) => {
      e.stopPropagation();
      setIsEditingTitle(true);
    }, []);

    const handleTitleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        setTitle(e.target.value);
      },
      []
    );

    const handleTitleBlur = useCallback(() => {
      setIsEditingTitle(false);
      onUpdate(data.id, { title });
    }, [data.id, onUpdate, title]);

    const handleTitleKeyPress = useCallback(
      (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === "Enter") {
          handleTitleBlur();
        }
      },
      [handleTitleBlur]
    );

    const handleContentChange = useCallback(
      (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setContent(e.target.value);
      },
      []
    );

    const handleContentBlur = useCallback(() => {
      onUpdate(data.id, { content });
    }, [content, data.id, onUpdate]);

    const handleDelete = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        onDelete(data.id);
      },
      [data.id, onDelete]
    );

    // 处理点击整个文本块的事件，提升其层级
    const handleTextBlockClick = useCallback(() => {
      onUpdate(data.id, { zIndex: Date.now() }); // 使用当前时间戳作为新的 zIndex 值
    }, [data.id, onUpdate]);

    return (
      <Rnd
        size={{ width, height }}
        position={{ x, y }}
        minWidth={100}
        minHeight={80}
        onDragStop={handleDragStop}
        onResizeStop={handleResizeStop}
        className="text-block"
        dragHandleClassName="text-block-header"
        data-id={data.id}
        style={{ zIndex: data.zIndex || 1 }}
        resizeHandleStyles={{
          bottomRight: {
            width: "20px",
            height: "20px",
            right: "0",
            bottom: "0",
            cursor: "nwse-resize",
          },
        }}
        bounds="parent" // 限制在父容器内
        enableResizing={{
          // 只允许从右下角调整大小
          top: false,
          right: false,
          bottom: false,
          left: false,
          topRight: false,
          bottomRight: true,
          bottomLeft: false,
          topLeft: false,
        }}
        onMouseDown={handleTextBlockClick} // 点击时提升层级
        scale={1} // 固定缩放值，避免额外计算
        dragAxis="both" // 允许横向和纵向拖动
        disableDragging={false} // 明确启用拖动
        cancel=".text-block-content, .text-block-title-input, .text-block-delete-button" // 指定哪些元素不触发拖动
        enableUserSelectHack={false} // 禁用用户选择文本时的浏览器默认行为
        default={{
          // 设置默认值，确保初始渲染一致
          x,
          y,
          width,
          height,
        }}
      >
        <div
          className="text-block-header"
          onDoubleClick={handleTitleDoubleClick}
        >
          {isEditingTitle ? (
            <Input
              value={title}
              onChange={handleTitleChange}
              onBlur={handleTitleBlur}
              onKeyPress={handleTitleKeyPress}
              autoFocus
              size="small"
              className="text-block-title-input"
              onClick={(e) => e.stopPropagation()} // 防止点击输入框时触发画布的拖拽
            />
          ) : (
            <span className="text-block-title">{title}</span>
          )}
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={handleDelete}
            size="small"
            className="text-block-delete-button"
          />
        </div>
        <textarea
          ref={textareaRef}
          value={content}
          onChange={handleContentChange}
          onBlur={handleContentBlur}
          className="text-block-content"
          placeholder="演示文本"
          onClick={(e) => e.stopPropagation()} // 防止点击文本域时触发画布的拖拽
          style={{
            height: height - 30 + "px", // 减去标题栏高度
            width: "100%",
            resize: "none",
          }}
        />
      </Rnd>
    );
  }
);

// 添加显示名称，便于调试
TextBlock.displayName = "TextBlock";

export default TextBlock;
