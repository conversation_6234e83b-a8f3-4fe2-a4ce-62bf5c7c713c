import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import StickyNote, { StickyNoteData } from './StickyNote';

// 模拟 react-rnd
jest.mock('react-rnd', () => {
  return {
    Rnd: ({ children, ...props }: any) => (
      <div data-testid="rnd-container" {...props}>
        {children}
      </div>
    ),
  };
});

describe('StickyNote 组件', () => {
  const mockData: StickyNoteData = {
    id: 'test-note-1',
    title: '测试便签',
    content: '这是测试内容',
    x: 100,
    y: 100,
    width: 200,
    height: 150,
    color: '#fff59d',
  };

  const mockOnUpdate = jest.fn();
  const mockOnDelete = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('应该正确渲染便签', () => {
    render(
      <StickyNote
        data={mockData}
        onUpdate={mockOnUpdate}
        onDelete={mockOnDelete}
        scale={1}
      />
    );

    // 检查标题是否显示
    expect(screen.getByText('测试便签')).toBeInTheDocument();
    
    // 检查内容是否显示
    expect(screen.getByDisplayValue('这是测试内容')).toBeInTheDocument();
    
    // 检查删除按钮是否存在
    expect(screen.getByTitle('删除便签')).toBeInTheDocument();
  });

  test('应该能够编辑标题', () => {
    render(
      <StickyNote
        data={mockData}
        onUpdate={mockOnUpdate}
        onDelete={mockOnDelete}
        scale={1}
      />
    );

    // 点击标题开始编辑
    const titleElement = screen.getByText('测试便签');
    fireEvent.click(titleElement);

    // 应该显示输入框
    const titleInput = screen.getByDisplayValue('测试便签');
    expect(titleInput).toBeInTheDocument();

    // 修改标题
    fireEvent.change(titleInput, { target: { value: '新标题' } });
    fireEvent.blur(titleInput);

    // 应该调用更新函数
    expect(mockOnUpdate).toHaveBeenCalledWith('test-note-1', { title: '新标题' });
  });

  test('应该能够编辑内容', () => {
    render(
      <StickyNote
        data={mockData}
        onUpdate={mockOnUpdate}
        onDelete={mockOnDelete}
        scale={1}
      />
    );

    // 找到内容文本区域
    const contentTextarea = screen.getByDisplayValue('这是测试内容');
    
    // 修改内容
    fireEvent.change(contentTextarea, { target: { value: '新的内容' } });

    // 应该调用更新函数
    expect(mockOnUpdate).toHaveBeenCalledWith('test-note-1', { content: '新的内容' });
  });

  test('应该能够删除便签', () => {
    render(
      <StickyNote
        data={mockData}
        onUpdate={mockOnUpdate}
        onDelete={mockOnDelete}
        scale={1}
      />
    );

    // 点击删除按钮
    const deleteButton = screen.getByTitle('删除便签');
    fireEvent.click(deleteButton);

    // 应该调用删除函数
    expect(mockOnDelete).toHaveBeenCalledWith('test-note-1');
  });

  test('按 Enter 键应该完成标题编辑', () => {
    render(
      <StickyNote
        data={mockData}
        onUpdate={mockOnUpdate}
        onDelete={mockOnDelete}
        scale={1}
      />
    );

    // 点击标题开始编辑
    const titleElement = screen.getByText('测试便签');
    fireEvent.click(titleElement);

    // 修改标题并按 Enter
    const titleInput = screen.getByDisplayValue('测试便签');
    fireEvent.change(titleInput, { target: { value: '新标题' } });
    fireEvent.keyDown(titleInput, { key: 'Enter' });

    // 应该调用更新函数
    expect(mockOnUpdate).toHaveBeenCalledWith('test-note-1', { title: '新标题' });
  });

  test('按 Escape 键应该取消标题编辑', () => {
    render(
      <StickyNote
        data={mockData}
        onUpdate={mockOnUpdate}
        onDelete={mockOnDelete}
        scale={1}
      />
    );

    // 点击标题开始编辑
    const titleElement = screen.getByText('测试便签');
    fireEvent.click(titleElement);

    // 修改标题并按 Escape
    const titleInput = screen.getByDisplayValue('测试便签');
    fireEvent.change(titleInput, { target: { value: '新标题' } });
    fireEvent.keyDown(titleInput, { key: 'Escape' });

    // 不应该调用更新函数
    expect(mockOnUpdate).not.toHaveBeenCalled();
  });
});
